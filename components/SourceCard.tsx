import { <PERSON><PERSON><PERSON>, Play, Code2, ExternalLink, Clock } from "lucide-react";
import { CardEnhanced } from "@/components/ui/card-enhanced";
import { BadgeEnhanced } from "@/components/ui/badge-enhanced";
import { utils } from "@/lib/design-system";

interface SourceCardProps {
  technology: string;
  lessonNumber: string;
  lessonTopic: string;
  startTime: string;
  endTime: string;
}

export const SourceCard = ({
  technology,
  lessonNumber,
  lessonTopic,
  startTime,
  endTime
}: SourceCardProps) => {
  // Enhanced technology badge colors with gradients
  const getTechBadgeColor = (tech: string) => {
    const colors: Record<string, string> = {
      python: 'bg-gradient-to-r from-blue-500 to-blue-600',
      javascript: 'bg-gradient-to-r from-yellow-500 to-yellow-600',
      react: 'bg-gradient-to-r from-cyan-500 to-cyan-600',
      nodejs: 'bg-gradient-to-r from-green-500 to-green-600',
      typescript: 'bg-gradient-to-r from-blue-600 to-blue-700',
      html: 'bg-gradient-to-r from-orange-500 to-orange-600',
      css: 'bg-gradient-to-r from-purple-500 to-purple-600',
      vue: 'bg-gradient-to-r from-emerald-500 to-emerald-600',
      angular: 'bg-gradient-to-r from-red-500 to-red-600',
      default: 'bg-gradient-to-r from-gray-500 to-gray-600'
    };
    return colors[tech.toLowerCase()] || colors.default;
  };

  // Calculate duration
  const calculateDuration = (start: string, end: string) => {
    try {
      const startTime = start.split(':').map(Number);
      const endTime = end.split(':').map(Number);
      const startSeconds = startTime[0] * 60 + startTime[1];
      const endSeconds = endTime[0] * 60 + endTime[1];
      const durationSeconds = endSeconds - startSeconds;
      const minutes = Math.floor(durationSeconds / 60);
      const seconds = durationSeconds % 60;
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    } catch {
      return '0:00';
    }
  };

  return (
    <CardEnhanced
      variant="interactive"
      className={utils.cn("group cursor-pointer", "hover:scale-[1.02]")}
      role="button"
      aria-label={`Jump to ${lessonTopic} at ${startTime}`}
    >
      <div className="flex items-start gap-4">
        {/* Enhanced Icon */}
        <div className="relative">
          <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 shadow-lg group-hover:shadow-orange-500/25 transition-all duration-200">
            <BookOpen className="h-5 w-5 text-white" />
          </div>
          <div className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-green-500 border-2 border-gray-900 animate-pulse"></div>
        </div>

        <div className="flex-1 min-w-0">
          {/* Technology Badge */}
          <div className="flex items-center gap-2 mb-3">
            <BadgeEnhanced
              variant="primary"
              size="sm"
              icon={<Code2 className="h-3.5 w-3.5" />}
              className={getTechBadgeColor(technology)}
            >
              {technology.toUpperCase()}
            </BadgeEnhanced>
            <div className="flex items-center gap-1 text-xs text-gray-500">
              <Clock className="h-3 w-3" />
              <span>{calculateDuration(startTime, endTime)}</span>
            </div>
          </div>

          {/* Enhanced Lesson Info */}
          <div className="mb-3">
            <h4 className="text-sm font-semibold text-gray-100 mb-1 leading-tight group-hover:text-white transition-colors">
              Lesson {lessonNumber}
            </h4>
            <p className="text-sm text-gray-300 leading-relaxed line-clamp-2">
              {lessonTopic}
            </p>
          </div>

          {/* Enhanced Time Range */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-xs text-gray-400">
              <div className="flex h-6 w-6 items-center justify-center rounded-lg bg-gradient-to-br from-gray-700 to-gray-800 group-hover:from-orange-500/20 group-hover:to-orange-600/20 transition-all duration-200">
                <Play className="h-3 w-3 text-orange-400" />
              </div>
              <span className="font-mono font-medium">
                {startTime} → {endTime}
              </span>
            </div>

            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <ExternalLink className="h-4 w-4 text-orange-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Subtle bottom border for visual separation */}
      <div className="mt-3 pt-3 border-t border-gray-800/50">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>Click to jump to timestamp</span>
          <span className="text-orange-400">•</span>
        </div>
      </div>
    </CardEnhanced>
  );
};