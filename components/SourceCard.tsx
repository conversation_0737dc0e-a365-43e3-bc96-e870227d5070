import { BookOpen, Play, Code2 } from "lucide-react";

interface SourceCardProps {
  technology: string;
  lessonNumber: string;
  lessonTopic: string;
  startTime: string;
  endTime: string;
}

export const SourceCard = ({
  technology,
  lessonNumber,
  lessonTopic,
  startTime,
  endTime
}: SourceCardProps) => {
  // Get technology badge color
  const getTechBadgeColor = (tech: string) => {
    const colors: Record<string, string> = {
      python: 'bg-blue-500',
      javascript: 'bg-yellow-500',
      react: 'bg-cyan-500',
      nodejs: 'bg-green-500',
      typescript: 'bg-blue-600',
      html: 'bg-orange-500',
      css: 'bg-purple-500',
      default: 'bg-gray-500'
    };
    return colors[tech.toLowerCase()] || colors.default;
  };

  return (
    <div
      className="rounded-xl border border-gray-700 p-4 backdrop-blur-sm transition-all duration-200 hover:border-orange-500/50"
      style={{ backgroundColor: '#1a1a1a' }}
    >
      <div className="flex items-start gap-3">
        <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-r from-orange-500 to-orange-600">
          <BookOpen className="h-4 w-4 text-white" />
        </div>
        <div className="flex-1 min-w-0">
          {/* Technology Badge */}
          <div className="flex items-center gap-2 mb-2">
            <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium text-white ${getTechBadgeColor(technology)}`}>
              <Code2 className="h-3 w-3" />
              {technology}
            </span>
          </div>

          {/* Lesson Info */}
          <div className="text-sm font-medium text-gray-100 mb-2 leading-relaxed">
            Lesson {lessonNumber} - {lessonTopic}
          </div>

          {/* Time Range */}
          <div className="flex items-center gap-2 text-xs text-gray-400">
            <div className="flex h-5 w-5 items-center justify-center rounded" style={{ backgroundColor: '#2a2a2a' }}>
              <Play className="h-3 w-3 text-orange-400" />
            </div>
            <span className="font-mono">{startTime} → {endTime}</span>
          </div>
        </div>
      </div>
    </div>
  );
};