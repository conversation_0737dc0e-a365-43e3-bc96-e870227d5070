"use client";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useState, useRef, useEffect, JSX } from "react";
import { Send, User, Code2 } from "lucide-react";
import { SourceCard } from "@/components/SourceCard";
import { Prism as Syntax<PERSON>ighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import Image from "next/image";

interface Source {
  id: string;
  title: string;
  timestamp?: string;
  timeRanges?: string[];
}

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  sources?: Source[];
}

// Function to parse and render message content with code highlighting
const renderMessageContent = (content: string) => {
  const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;

  let lastIndex = 0;
  const elements: JSX.Element[] = [];
  let match;

  // Handle code blocks
  while ((match = codeBlockRegex.exec(content)) !== null) {
    // Add text before code block
    if (match.index > lastIndex) {
      const textBefore = content.slice(lastIndex, match.index);
      elements.push(
        <span key={`text-${lastIndex}`} className="whitespace-pre-wrap">
          {renderInlineCode(textBefore)}
        </span>,
      );
    }

    // Add code block
    const language = match[1] || "text";
    const code = match[2];
    elements.push(
      <div key={`code-${match.index}`} className="my-3">
        <div className="flex items-center gap-2 rounded-t-lg bg-gray-800 px-3 py-2">
          <Code2 className="h-4 w-4 text-orange-400" />
          <span className="text-xs font-medium text-gray-300 uppercase">
            {language}
          </span>
        </div>
        <SyntaxHighlighter
          language={language}
          style={vscDarkPlus}
          customStyle={{
            margin: 0,
            borderTopLeftRadius: 0,
            borderTopRightRadius: 0,
            borderBottomLeftRadius: "0.5rem",
            borderBottomRightRadius: "0.5rem",
            fontSize: "0.875rem",
          }}
        >
          {code}
        </SyntaxHighlighter>
      </div>,
    );

    lastIndex = match.index + match[0].length;
  }

  // Add remaining text
  if (lastIndex < content.length) {
    const remainingText = content.slice(lastIndex);
    elements.push(
      <span key={`text-${lastIndex}`} className="whitespace-pre-wrap">
        {renderInlineCode(remainingText)}
      </span>,
    );
  }

  return elements.length > 0 ? elements : renderInlineCode(content);
};

// Function to handle inline code
const renderInlineCode = (text: string) => {
  const inlineCodeRegex = /`([^`]+)`/g;
  const parts = [];
  let lastIndex = 0;
  let match;

  while ((match = inlineCodeRegex.exec(text)) !== null) {
    if (match.index > lastIndex) {
      parts.push(text.slice(lastIndex, match.index));
    }
    parts.push(
      <code
        key={`inline-${match.index}`}
        className="rounded bg-gray-800 px-1.5 py-0.5 font-mono text-sm text-orange-300"
      >
        {match[1]}
      </code>,
    );
    lastIndex = match.index + match[0].length;
  }

  if (lastIndex < text.length) {
    parts.push(text.slice(lastIndex));
  }

  return parts.length > 1 ? parts : text;
};

const HomePage = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = async (content: string) => {
    if (!content.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content,
    };

    setMessages((prev) => [...prev, userMessage]);
    setLoading(true);

    try {
      const response = await fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          messages: [...messages, userMessage].map((m) => ({
            role: m.role,
            content: m.content,
          })),
        }),
      });

      if (!response.body) throw new Error("No response body");

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      let assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: "",
        sources: [],
      };

      setMessages((prev) => [...prev, assistantMessage]);

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split("\n");

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const data = JSON.parse(line.slice(6));

              if (data.type === "sources") {
                setMessages((prev) =>
                  prev.map((msg) =>
                    msg.id === assistantMessage.id
                      ? { ...msg, sources: data.data }
                      : msg,
                  ),
                );
              } else if (data.type === "text") {
                setMessages((prev) =>
                  prev.map((msg) =>
                    msg.id === assistantMessage.id
                      ? { ...msg, content: msg.content + data.data }
                      : msg,
                  ),
                );
              } else if (data.type === "done") {
                setLoading(false);
              } else if (data.type === "error") {
                console.error("Stream error:", data.data);
                setLoading(false);
              }
            } catch (e) {
              // Ignore parsing errors for incomplete chunks
              console.debug("Chunk parsing error (expected for incomplete data):", e);
            }
          }
        }
      }
    } catch (error) {
      console.error("Error:", error);
      setLoading(false);

      // Add error message to chat
      const errorMessage: Message = {
        id: (Date.now() + 2).toString(),
        role: "assistant",
        content: "Sorry, I encountered an error while processing your request. Please try again.",
      };
      setMessages((prev) => [...prev, errorMessage]);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    sendMessage(input);
    setInput("");
  };

  return (
    <div
      className="flex h-screen flex-col"
      style={{ backgroundColor: "#0a0a0a" }}
    >
      {/* Header */}
      <div
        className="border-b border-gray-800 px-6 py-4 shadow-lg"
        style={{ backgroundColor: "#1a1a1a" }}
      >
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-full overflow-hidden">
            <Image
              src="/chai.webp"
              width={40}
              height={40}
              alt="ChaiCourseGPT logo"
              className="object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
              }}
            />
          </div>
          <div>
            <h1 className="text-xl font-bold text-white">ChaiCourseGPT</h1>
            <p className="text-sm text-gray-400">Your AI coding instructor</p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div
        className="flex-1 overflow-y-auto px-4 py-6"
        style={{ backgroundColor: "#0a0a0a" }}
      >
        {messages.length === 0 ? (
          <div className="flex h-full items-center justify-center">
            <div className="text-center">
              <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full shadow-lg overflow-hidden ">
                <Image
                  src="/chai.webp"
                  width={80}
                  height={80}
                  alt="ChaiCourseGPT logo"
                  className="object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
              </div>
              <h2 className="mb-2 text-xl font-semibold text-white">
                Welcome to ChaiCourseGPT
              </h2>
              <p className="text-gray-400">
                Ask me anything about your course content
              </p>
            </div>
          </div>
        ) : (
          <div className="mx-auto max-w-4xl space-y-6">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-4 ${
                  message.role === "user" ? "justify-end" : "justify-start"
                }`}
              >
                {message.role === "assistant" && (
                  <div className="flex-shrink-0">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full shadow-md overflow-hidden bg-gradient-to-r from-orange-500 to-orange-600">
                      <Image
                        src="/hiteshchoudhary.webp"
                        height={32}
                        width={32}
                        alt="AI Assistant"
                        className="object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          // Show fallback Bot icon
                          const parent = target.parentElement;
                          if (parent) {
                            parent.innerHTML = '<svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2z"></path></svg>';
                          }
                        }}
                      />
                    </div>
                  </div>
                )}

                <div className="max-w-xs sm:max-w-md md:max-w-lg lg:max-w-3xl">
                  {/* Message Content */}
                  <div
                    className={`rounded-2xl px-4 py-3 shadow-md ${
                      message.role === "user"
                        ? "bg-gradient-to-r from-orange-500 to-orange-600 text-white"
                        : "border border-gray-700 text-gray-100"
                    }`}
                    style={
                      message.role === "assistant"
                        ? { backgroundColor: "#1a1a1a" }
                        : {}
                    }
                  >
                    <div className="text-sm leading-relaxed">
                      {message.content
                        ? renderMessageContent(message.content)
                        : null}
                    </div>
                  </div>

                  {/* Sources Below Message */}
                  {message.role === "assistant" &&
                    message.sources &&
                    message.sources.length > 0 && (
                      <div className="mt-4">
                        <div className="mb-3 flex items-center gap-2">
                          <div className="h-px flex-1 bg-gray-700"></div>
                          <span className="text-xs font-medium tracking-wider text-gray-400 uppercase">
                            Sources ({message.sources.length})
                          </span>
                          <div className="h-px flex-1 bg-gray-700"></div>
                        </div>
                        <div className="space-y-2">
                          {message.sources.map((source) => (
                            <SourceCard
                              key={source.id}
                              title={source.title}
                              timestamp={source.timestamp}
                              timeRanges={source.timeRanges}
                            />
                          ))}
                        </div>
                      </div>
                    )}
                </div>

                {message.role === "user" && (
                  <div className="flex-shrink-0">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-700 shadow-md">
                      <Image src="/hiteshchoudhary.webp" height={100} width={100} alt="hiteshchoudhary"/>
                    </div>
                  </div>
                )}
              </div>
            ))}

            {loading && (
              <div className="flex justify-start gap-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-r from-orange-500 to-orange-600 shadow-md overflow-hidden">
                  <Image
                    src="/hiteshchoudhary.webp"
                    height={32}
                    width={32}
                    alt="AI Assistant"
                    className="object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      // Show fallback Bot icon
                      const parent = target.parentElement;
                      if (parent) {
                        parent.innerHTML = '<svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2z"></path></svg>';
                      }
                    }}
                  />
                </div>
                <div
                  className="rounded-2xl border border-gray-700 px-4 py-3 shadow-md"
                  style={{ backgroundColor: "#1a1a1a" }}
                >
                  <div className="flex space-x-1">
                    <div className="h-2 w-2 animate-bounce rounded-full bg-orange-500"></div>
                    <div
                      className="h-2 w-2 animate-bounce rounded-full bg-orange-500"
                      style={{ animationDelay: "0.1s" }}
                    ></div>
                    <div
                      className="h-2 w-2 animate-bounce rounded-full bg-orange-500"
                      style={{ animationDelay: "0.2s" }}
                    ></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div
        className="border-t border-gray-800 px-4 py-4"
        style={{ backgroundColor: "#1a1a1a" }}
      >
        <form className="mx-auto flex max-w-4xl gap-2 sm:gap-3" onSubmit={handleSubmit}>
          <div className="relative flex-1">
            <Textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              disabled={loading}
              className="max-h-32 min-h-[52px] resize-none rounded-xl border-gray-600 text-white placeholder-gray-400 focus:border-orange-500 focus:ring-orange-500/20 disabled:opacity-50 disabled:cursor-not-allowed"
              style={{ backgroundColor: "#2a2a2a" }}
              placeholder={loading ? "Processing your message..." : "Ask me about the course content..."}
              onKeyDown={(e) => {
                if (e.key === "Enter" && !e.shiftKey && !loading) {
                  e.preventDefault();
                  handleSubmit(e);
                }
              }}
            />
          </div>
          <Button
            type="submit"
            disabled={loading || !input.trim()}
            className="h-[52px] w-[52px] rounded-xl bg-gradient-to-r from-orange-500 to-orange-600 shadow-lg transition-all duration-200 hover:from-orange-600 hover:to-orange-700 hover:shadow-xl disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed disabled:shadow-none"
            title={loading ? "Processing..." : "Send message"}
          >
            <Send className={`h-5 w-5 transition-transform duration-200 ${loading ? 'animate-pulse' : ''}`} />
          </Button>
        </form>
        <div className="mt-2 text-center">
          <p className="text-xs text-gray-500">
            Press Enter to send, Shift + Enter for new line
          </p>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
